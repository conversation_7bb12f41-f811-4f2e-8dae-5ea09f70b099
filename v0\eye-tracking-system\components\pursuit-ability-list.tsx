"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Search, Eye, FileText, Clock, User, Activity, Move } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import apiService, { PursuitAbilityRecord, PageResponse, PageParams } from "@/lib/api"

interface PursuitAbilityListProps {
  onViewDetail?: (record: PursuitAbilityRecord) => void
}

export default function PursuitAbilityList({ onViewDetail }: PursuitAbilityListProps) {
  const [records, setRecords] = useState<PursuitAbilityRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
    total: 0,
    pages: 0
  })
  
  // 搜索参数
  const [searchParams, setSearchParams] = useState<PageParams>({
    current: 1,
    size: 10,
    patientName: '',
    status: '',
  })

  const { toast } = useToast()

  // 获取数据
  const fetchData = async (params: PageParams = searchParams) => {
    setLoading(true)
    try {
      // 使用示例API URL，实际使用时需要配置正确的baseURL
      const response = await apiService.getPursuitAbilityList(params)
      setRecords(response.data.records)
      setPagination({
        current: response.data.current,
        size: response.data.size,
        total: response.data.total,
        pages: response.data.pages
      })
    } catch (error) {
      toast({
        title: "获取数据失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取追随能力测试数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    fetchData()
  }, [])

  // 搜索处理
  const handleSearch = () => {
    const newParams = { ...searchParams, current: 1 }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 重置搜索
  const handleReset = () => {
    const resetParams = {
      current: 1,
      size: 10,
      patientName: '',
      status: '',
    }
    setSearchParams(resetParams)
    fetchData(resetParams)
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    const newParams = { ...searchParams, current: page }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 页面大小变化
  const handlePageSizeChange = (size: number) => {
    const newParams = { ...searchParams, size, current: 1 }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Move className="w-6 h-6 text-green-600" />
            追随能力测试
          </h1>
          <p className="text-gray-600 mt-1">眼球追随运动能力评估与分析</p>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            搜索筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">患者姓名</label>
              <Input
                placeholder="输入患者姓名"
                value={searchParams.patientName || ''}
                onChange={(e) => setSearchParams({ ...searchParams, patientName: e.target.value })}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">测试状态</label>
              <Select
                value={searchParams.status || ''}
                onValueChange={(value) => setSearchParams({ ...searchParams, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部状态</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="PROCESSING">处理中</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleSearch} className="flex items-center gap-2">
                <Search className="w-4 h-4" />
                搜索
              </Button>
              <Button variant="outline" onClick={handleReset}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            测试记录列表
          </CardTitle>
          <CardDescription>
            共 {pagination.total} 条记录，第 {pagination.current} / {pagination.pages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>患者信息</TableHead>
                      <TableHead>测试序号</TableHead>
                      <TableHead>测试时间</TableHead>
                      <TableHead>时长</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>追随精度</TableHead>
                      <TableHead>平均速度</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {records.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                          暂无数据
                        </TableCell>
                      </TableRow>
                    ) : (
                      records.map((record) => (
                        <TableRow key={record.id} className="hover:bg-gray-50">
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4 text-gray-400" />
                                <span className="font-medium">{record.patientName}</span>
                              </div>
                              <div className="text-sm text-gray-500">
                                住院号: {record.inpatientNum}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-mono text-sm">
                              {record.testSequence}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center gap-1">
                                <Calendar className="w-4 h-4 text-gray-400" />
                                <span className="text-sm">
                                  {new Date(record.testDate).toLocaleDateString()}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-500">
                                  {new Date(record.testDate).toLocaleTimeString()}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Activity className="w-4 h-4 text-gray-400" />
                              <span>{formatDuration(record.duration)}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(record.status)}>
                              {record.statusDesc}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-center">
                              <div className="font-medium text-green-600">
                                {record.pursuitAccuracy?.toFixed(1)}%
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-center">
                              <div className="font-medium">
                                {record.averageVelocity?.toFixed(1)} °/s
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              onClick={() => onViewDetail?.(record)}
                              className="flex items-center gap-1"
                            >
                              <Eye className="w-4 h-4" />
                              查看详情
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* 分页控件 */}
              {pagination.total > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">每页显示</span>
                    <Select
                      value={pagination.size.toString()}
                      onValueChange={(value) => handlePageSizeChange(parseInt(value))}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-sm text-gray-600">条记录</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.current - 1)}
                      disabled={pagination.current <= 1}
                    >
                      上一页
                    </Button>
                    
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <Button
                            key={page}
                            variant={pagination.current === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                            className="w-8 h-8 p-0"
                          >
                            {page}
                          </Button>
                        )
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.current + 1)}
                      disabled={pagination.current >= pagination.pages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
